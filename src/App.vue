<template>
  <div>
    <phone v-if="isMobile"></phone>
    <div class="qiankun-mainPart" v-else-if="hostMap" :class="[localPathname, isNotebook && 'is-notebook']"
      v-loading="systemLoading" element-loading-background="rgba(0,0,0,0)" element-loading-spinner="empty-loading">
      <new-menu  class="qiankun-layout-left"  v-if="showMenu" :collapse.sync="collapse"></new-menu>
      <div class="qiankun-layout-right">
        <topBar :hostMap="hostMap" :active="active" :navs="navs" :goFeishuChart="goFeishuChart" />
        <Home v-if="!localPathname" />
        <div class="qiankun-core-content">
          <div class="qiankun-content-view">
            <pageNav :collapse.sync="collapse" v-if="hasUserInfo && hasAppPermission" />
            <div class="qiankun-content-inner" v-loading="systemLoading"  element-loading-background="rgba(0,0,0,0)">
              <qiankun-iframe v-if="hasUserInfo" />
              <div id="qiankun-dom-cache"></div>
              <div id="vue" ref="appRoot"></div>
            </div>
          </div>
        </div>
      </div>
      <noRootPage :adminMap="adminMap" :goFeishuChart="goFeishuChart" ref="noRootPage" :collapse="collapse"></noRootPage>
    </div>
  </div>
</template>

<script>
// import lowcodePage from './lowcodePage'
import phone from './views/app/phone.vue'
import lowcodelib from 'lowcodelib/components/'
import MINXIN_MENU from './mixins/menu'
import MINXIN_LOAD_SYS from './mixins/loadSys'
import Vue from 'vue'
import Home from '@/views/Home'
import Page404 from '@/views/error-page/404'
import topBar from '@/views/app/topBar'
import noRootPage from '@/views/app/noRootPage'
import NewMenu from '@/views/app/menu/new'
import syIframeAlive from '@/views/components/sy-iframe-alive.vue'
import pageNav from '@/views/app/pageNav'
// import preview from '@/views/app/preview'
import utilsFile from '@/utils/file'
// import CpDebunk from '@/views/components/cp-debunk'
import MINXIN_STAGING from '@/views/components/staging/minxin.js'
import qiankunIframe from '@/views/components/qiankun-iframe'
import {
  start,
  loadMicroApp,
  initGlobalState,
  registerMicroApps,
  prefetchApps
} from 'qiankun'
import utils from '@/utils'
import FEISHULINK from './mixins/feishuLink'

if (!window.QIANKUN_DATA) {
  window.QIANKUN_DATA = {}
}

initQianKunData(location.pathname)

function initQianKunData (pathname) {
  const localPathname = pathname.replace(/\//g, '')
  const isLocal = location.host === 'devhermes.syounggroup.com'

  const INSERT_URL =
    process.env.VUE_APP_MODE === 'production'
      ? '//inset-hermes.syounggroup.com'
      : '//inset-testhermes.syounggroup.com'

  let subOrigin = INSERT_URL // 子项目域名地址

  if (isLocal) {
    subOrigin = 'http://localhost:9001'
  }

  if (sessionStorage.qiankunOrigin) {
    subOrigin = sessionStorage.qiankunOrigin
  }
  if (localStorage.qiankunOrigin) {
    subOrigin = localStorage.qiankunOrigin
  }

  // 乾坤全局数据
  mergeObj(
    window.QIANKUN_DATA,
    {
      origin: window.origin,
      isProdEnv: process.env.VUE_APP_MODE === 'production', // 是否线上环境
      localPathname: localPathname,
      subLocation: {
        // 子项目路径
        origin: subOrigin,
        pathname: `/${localPathname}`,
        href: `${subOrigin}/${localPathname}`
      },
      errorPage: [
        {
          path: '*',
          name: 'page404',
          component: Page404
        }
      ],
      COMPS: {
        syIframeAlive
      },
      BASE_URL: window.QIANKUN_SETTING.BASE_URL,
      components: {
        'sy-normal-table': lowcodelib
      }
    }
  )

  // 子项目
  const appConfig = {
    name: `hermes-${window.QIANKUN_DATA.subLocation.pathname.slice(1)}`,
    entry: `${window.QIANKUN_DATA.subLocation.href}?t=${Date.now()}`,
    container: '#vue',
    activeRule: window.QIANKUN_DATA.subLocation.pathname
  }
  window.QIANKUN_DATA.appConfig = appConfig
}

// 合并对象,数组直接覆盖，对象递归合并
function mergeObj (obj1, obj2) {
  for (const key in obj2) {
    const element = obj2[key]
    if (obj1[key] === undefined || Object.isFrozen(obj1[key])) {
      obj1[key] = element
    } else if (typeof element === 'object') {
      obj1[key] = mergeObj(obj1[key] || {}, element)
    } else {
      obj1[key] = element
    }
  }
  return obj1
}

// 配置进来时需要隐藏菜单的地址
const hideMenuSystemPath = []
// const hideMenuSystemPath = ['syoung-store-admin', 'brand-member', 'newretail']

prefetchApps([window.QIANKUN_DATA.appConfig])

export default {
  components: { topBar, pageNav, Home, NewMenu, noRootPage, qiankunIframe, phone },
  mixins: [MINXIN_MENU, FEISHULINK, MINXIN_STAGING, MINXIN_LOAD_SYS],
  data () {
    return {
      isMobile: window.screen.width < 800,
      systemLoading: false, // 系统加载中
      localPathname: window.QIANKUN_DATA.localPathname,
      collapse: localStorage.getItem('qiankunMenuCollapse') === 'true',
      hostMap: null,
      token: {},
      hasUserInfo: false,
      navs: [],
      showMenu: true,
      adminMap: {
        'hermes-workspace': '拉夫'
      }
    }
  },
  async created () {
    if (this.isMobile) {
      return
    }
    this.initClickBatch()
    window.QIANKUN_DATA.destroyChild = this.destroyChild.bind(this)
    window.QIANKUN_DATA.setCollapse = this.setCollapse.bind(this)
    window.QIANKUN_DATA.qiankunVm = this
    window.QIANKUN_DATA.qiankunVue = Vue
    window.QIANKUN_DATA.setTabListNameByCode = this.$store.commit.bind(this.$store, 'setTabListNameByCode')
    this.actions = initGlobalState(this.getGlobalState())
    await Promise.all([this.getNavs(), this.setHostMap()])
    await this.initSystem()
    this.$store.dispatch('flashCollectionList')
    this.onPageChange(this.$route)

    this.$router.beforeEach(async (to, from, next) => {
      // 限制只能打开15个标签页面
      const isSysHome = to.hash.replace('#/', '') === ''
      const limitNum = localStorage.coloseTabNavLimit ? 100 : 15
      if (!isSysHome && this.tabList.length >= limitNum && !this.tabList.find(it => it.pathname === to.path && it.fullPath === to.hash.replace('#', ''))) {
        this.openMoreTabsMsg()
        next(false)
        return false
      }
      if (to.path !== from.path) {
        initQianKunData(to.path)
        this.localPathname = window.QIANKUN_DATA.localPathname
        await this.initSystem(to, from)
      }
      this.onPageChange(to)
      next()
    })
  },
  computed: {
    // 标签页列表
    tabList () {
      return this.$store.state.pageNav
    },
    USER_INFO () {
      return this.$store.state.USER_INFO
    },
    pageNav () {
      return window.QIANKUN_STORE.state.pageNav
    },
    active () {
      for (const key in this.hostMap) {
        if (
          this.hostMap[key] && new URL(this.hostMap[key]).pathname.replace(/\//g, '') === this.localPathname
        ) {
          window.QIANKUN_DATA.active = key
          // this.setSysTitle(this.navs, window.QIANKUN_DATA.active)   // 长流建议，拉夫决策，统一叫HERMES
          return key
        }
      }
      return ''
    },
    debunk () {
      return {
        name: window.QIANKUN_DATA.user_info.user.name, // 中文花名
        system: this.active, // 系统名
        token: '',
        sy_baseUrl: window.QIANKUN_SETTING.BASE_URL
      }
    },
    // 是否有系统权限
    hasAppPermission () {
      if (!this.active) return false
      return window.QIANKUN_DATA.appList.includes(this.active)
    }
  },
  watch: {
    // 监听路有变化切换系统
    async $route (v, o) {
      // if (v.path !== o.path) {
      //   initQianKunData()
      //   this.localPathname = window.QIANKUN_DATA.localPathname
      //   // prefetchApps([window.QIANKUN_DATA.appConfig])
      //   setTimeout(() => {
      //     this.initSystem()
      //   }, 0)
      //   // this.initSystem()
      //   // setTimeout(() => {
      //   //   this.checkMemoryAndReload(1100)
      //   // }, 0)
      // }
    },
    collapse (v) {
      if (!this.stopSaveCollapse) {
        localStorage.setItem('qiankunMenuCollapse', v)
        this.stopSaveCollapse = false
      }

      this.setGlobState()
    },
    USER_INFO () {
      this.setGlobState()
    },
    pageNav () {
      this.setGlobState()
    }
  },
  methods: {
    // 初始化全局点击埋点事件
    initClickBatch () {
      document.body.addEventListener('click', (event) => {
        let target = event.target

        // 遍历点击元素的祖先节点，寻找带有reportName属性的节点
        while (target && target !== document.body) {
          if (target.hasAttribute('reportName')) {
            const reportName = target.getAttribute('reportName')
            const basicInfo = {
              name: reportName,
              menu_id: window.QIANKUN_STORE?.state?.activePageNav?.id,
              report_app_name: 'hermes',
              loginName: window.USER_INFO.user.loginName,
              userName: window.USER_INFO.user.name,
              report_app_tenant: window.QIANKUN_DATA.apiHeader['X-Tenant-Id'],
              report_app_ext_tenant: this.getExtTenant()
            }
            this.$upEvent('Click', { ...basicInfo })
            break
          }
          target = target.parentElement
        }
      })
    },
    openMoreTabsMsg: utils.throttle(function () {
      this.$alert('为了页面切换流畅，限制了允许打开的菜单数不超过15个，请关闭一些不用的页面', '系统提示', {
        confirmButtonText: '确定',
        type: 'warning'
      })
    }, 1500),
    onPageChange (route) {
      if (!this.hasAppPermission) return
      window.QIANKUN_STORE.commit('setActivePageNav', route) // 激活选中页面
      window.QIANKUN_STORE.commit('addPageNavByRoute', route) // 添加tab
      this.upEventPageView() // 页面统一埋点
    },
    setCollapse (collapse) {
      this.stopSaveCollapse = true
      this.collapse = collapse
    },
    checkMemoryAndReload (threshold) {
      // 检查performance.memory是否可用
      if (window.performance && window.performance.memory) {
        // 获取内存使用情况
        const usedJSHeapSize = window.performance.memory.usedJSHeapSize
        // 将字节转换为兆字节
        const usedJSHeapSizeMB = usedJSHeapSize / 1024 / 1024

        // 如果超过阈值，则刷新页面
        if (usedJSHeapSizeMB > threshold) {
          console.warn(`内存使用超过 ${threshold}MB，页面即将刷新`)
          window.location.reload()
        }
      } else {
        console.warn('浏览器不支持performance.memory，无法检测内存使用情况。')
      }
    },
    // async testBeforeUpload (obj) {
    //   const res = await utilsFile.uploadBigFile.call(this, obj.file, { isPrivate: true })
    //   const a = this.$api.fileFetchGetSignedUrl(res.res.requestUrls[0], 3000)
    // },
    // 首页跳转逻辑
    homePageGo () {
      // 如果在首页跳转到有权限的第一个页面
      // 1、收藏了指定系统的url，直接跳转至指定系统
      // 2、未收藏指定系统，且有工作台权限，跳工作台
      // 3、未收藏指定系统，且无工作台权限，保持现有逻辑，跳转他之前经常访问过的系统
      if (this.$route.path === '/') { // 首页跳转逻辑
        if (window.QIANKUN_DATA.firstApp.appId === 'hermes-workspace') {
          location.href = window.QIANKUN_DATA.firstApp.urlPath
        } else if (localStorage.qiankunBeforeAppUrl && window.QIANKUN_DATA.appList.includes(localStorage.qiankunBeforeAppName)) {
          location.href = localStorage.qiankunBeforeAppUrl
        } else {
          location.href = window.QIANKUN_DATA.firstApp.urlPath
        }
        return true
      } else if (!this.hasAppPermission) { // 直接访问子系统，如果没有权限，打开权限申请页面
        this.$refs.noRootPage.openNoRoot(this.active, false)
        return true
      } else {
        localStorage.qiankunBeforeAppUrl = location.href
        localStorage.qiankunBeforeAppName = this.active
      }
    },
    // 刷新系统
    async initSystem (to, from) {
      try {
        // 阻止子系统渲染，首页跳转逻辑
        if (this.homePageGo()) {
          // 清空菜单
          // this.$store.commit('setUserInfo', {
          //   ...window.QIANKUN_DATA.user_info,
          //   menus: []
          // })
          return
        }
        this.systemLoading = true
        this.showMenu = hideMenuSystemPath.indexOf(this.localPathname) === -1 // 是否显示菜单
        await Promise.all([this.setuserinfo(), this.initDict(this.active)])
        mergeObj(window.QIANKUN_DATA, this.getGlobalState())
        this.hasUserInfo = true
        this.setWatermark()
        this.runChildApp(to, from)
      } catch (error) {
        this.systemLoading = false
      }
    },
    // 获取顶部系统nav和对于的系统管理员
    async getNavs () {
      // const data = await Vue.prototype.$utils.axios.get(
      //   location.origin + '/top.json'
      // )
      // eslint-disable-next-line no-eval
      // this.navs = window.eval(`(${data})`).data
      this.navs = await this.$api.getTopJson()
      window.QIANKUN_DATA.topsJson = [...this.navs]
      this.getAdminMap(this.navs)
    },
    // 设置系统小标题名称
    setSysTitle (navs, active) {
      let list = [...navs]
      navs.forEach(it => {
        if (it.child) {
          list = [...list, ...it.child]
        }
      })
      const ret = list.find(it => it.value === active)
      if (ret) {
        document.title = `HERMES-${ret.label}`
      }
    },
    // 处理管理员映射表
    getAdminMap (navArr) {
      navArr.forEach((it) => {
        if (it.admin) this.adminMap[it.value] = it.admin
        if (it.child) this.getAdminMap(it.child)
      })
    },
    // 设置水印
    setWatermark () {
      if (!this.hasSetWatermark) {
        this.hasSetWatermark = true
        window.watermark.init({
          watermark_txt: 'HERMES:' + window.QIANKUN_DATA.user_info.user.name
        })
      }
    },
    upEventPageView () {
      const basicInfo = {
        name: window.QIANKUN_STORE?.state?.activePageNav?.name,
        menu_id: window.QIANKUN_STORE?.state?.activePageNav?.id,
        report_app_name: 'hermes',
        loginName: window.USER_INFO.user.loginName,
        userName: window.USER_INFO.user.name,
        report_app_tenant: window.QIANKUN_DATA.apiHeader['X-Tenant-Id'],
        report_app_ext_tenant: this.getExtTenant()
      }
      this.$upEvent('PageView', { ...basicInfo })
    },
    // 获取二级租户
    getExtTenant () {
      if (window.qiankunAppInfo && window.qiankunAppInfo.ext_tenant) { // 多租户系统
        return window.qiankunAppInfo.ext_tenant
      }
      return window.QIANKUN_DATA.apiHeader['X-Ext-Tenant-Id']
    },
    async setuserinfo (refreshToken = true, userVo) {
      const appIdMap = {
        'athena-service': 'athena-service',
        'oms-main-app': 'YJH',
        'mars-service': 'mars'
      }
      // 设置请求头，允许子系统覆盖

      window.QIANKUN_DATA.apiHeader = {
        'X-Ext-Tenant-Id': appIdMap[this.active] || 'defaultExtTenantId',
        'X-Tenant-Id': this.active
      }
      // 刷新令牌
      if (refreshToken) {
        this.token = await this.$api.createUserSession().catch(err => {
          this.$refs.noRootPage.openNoRoot(window.QIANKUN_DATA.active)
          console.log(err)
        })
        window.QIANKUN_DATA.userToken = this.token
      }
      // 刷新用户信息
      let USER_INFO
      if (userVo) {
        USER_INFO = userVo
      } else {
        USER_INFO = await this.$api.userVo(this.token)
      }
      // 冻结用户信息
      Object.freeze(USER_INFO.roles)
      Object.freeze(USER_INFO.user)

      // 设置qiankunKey
      USER_INFO.menus &&
        USER_INFO.menus.forEach((it) => {
          if (!it.qiankunKey) {
            it.qiankunKey = `${this.active}${it.code}+${it.href}` // 为了兼容子系统code码相同的情况
          }
          Object.freeze(it) // 冻结菜单属性提升性能
        })

      window.QIANKUN_DATA.user_info = JSON.parse(JSON.stringify(USER_INFO)) // 避免子系统形成依赖错误的更新页面，如权限判断
      // 未配置菜单权限
      if (USER_INFO && (!USER_INFO.menus || !USER_INFO.menus.length)) {
        this.$refs.noRootPage.openNoRoot(this.active)
        throw new Error('暂无权限')
      }
      this.$store.commit('setUserInfo', USER_INFO)
      if (!this.flashPageNav) {
        this.$store.commit('flashPageNav')
        this.flashPageNav = true
      }
    },
    // 初始化字典
    async initDict (active) {
      const dictMap = {}
      const data = await Vue.prototype.$api.getdataDictionary(active)
      data.forEach((it) => {
        if (!dictMap[it.type]) {
          dictMap[it.type] = []
        }
        dictMap[it.type].push({ label: it.label, value: it.value })
      })
      window.QIANKUN_DATA.dictMap = Object.freeze(dictMap)
      window.QIANKUN_DATA.dict = Object.freeze(data)
    },
    async setHostMap () {
      const appList = await this.$api.getTenantIdsByVirtualTenantId()
      const res = await this.$api.toolAppListAll()
      this.hostMap = {}
      res.forEach((it) => {
        if (!window.QIANKUN_DATA.firstApp && appList.includes(it.appId)) {
          window.QIANKUN_DATA.firstApp = it
        }
        this.hostMap[it.appId] = it.urlPath
      })
      window.QIANKUN_DATA.hostMap = { ...this.hostMap }
      window.QIANKUN_DATA.appList = [...appList]
    },
    setMenu ({ Menu, childVm, ChildVue = Vue, hideMenu } = {}) {
      // const that = this
      // return new Promise((resolve, reject) => {
      //   that.showMenu = !hideMenu
      //   if (that.menuVm) {
      //     that.menuVm.$destroy()
      //     that.menuVm = null
      //   }
      //   if (hideMenu) {
      //     return false
      //   }
      //   const MenuConstructor = ChildVue.extend(Menu)
      //   utils.untilRunFn(
      //     () => document.getElementById('menu'),
      //     () => {
      //       that.menuVm = new MenuConstructor({
      //         el: document.createElement('div'),
      //         propsData: {
      //           menus: window.QIANKUN_DATA.user_info.menus,
      //           vm: that,
      //           childVm
      //         }
      //       })
      //       that.menuVm.collapse = that.collapse
      //       document.getElementById('menu').innerHTML = ''
      //       document.getElementById('menu').append(that.menuVm.$el)
      //       resolve()
      //     }
      //   )
      // })
    },
    // 插入函数
    appendUtils (ChildVue) {
      ChildVue.prototype.$uploadBigFile = utilsFile.uploadBigFile.bind(this) // 下载大文件
      ChildVue.prototype.$getPrivateUrl = this.$api.fileFetchGetSignedUrl // 获取文件私有地址
      ChildVue.prototype.preview = this.$preview
      ChildVue.prototype.$downloadWatermarkFile = utilsFile.downloadWatermarkFile.bind(this) // 预览文件

      window.QIANKUN_DATA.$goFeishuRobotChat = this.goFeishuRobotChat.bind(this)
      setTimeout(() => {
        // ChildVue.prototype.$preview('https://oss.syounggroup.com/static/project/hermes/home-top-demo.png')
        // ChildVue.prototype.$preview('https://testoss.syounggroup.com/static/file/defaultTenantId/测试/docx预览.docx')
        // ChildVue.prototype.$preview('https://testoss.syounggroup.com/static/file/defaultTenantId/视频/预览.ogv')
        // ChildVue.prototype.$preview('https://testoss.syounggroup.com/static/file/defaultTenantId/音频.ogg')
        // ChildVue.prototype.$preview('https://testoss.syounggroup.com/static/file/defaultTenantId/测试/音频.mp3')
        // ChildVue.prototype.$preview(['https://oss.syounggroup.com/static/project/hermes/home-top-demo.png'], 'image')
        // ChildVue.prototype.$preview('https://testoss.syounggroup.com/static/file/defaultTenantId/测试/测试PDF.pdf')
        // ChildVue.prototype.$preview('https://testoss.syounggroup.com/static/file/athena-service/达人广场/导入模板_达人批量导入.xlsx')
        // ChildVue.prototype.$preview('https://testoss.syounggroup.com/amb-service/amb-service/22052616083389038768837674.pdf?asdasd=123123')
        // ChildVue.prototype.$preview('https://oss-private.syounggroup.com/amb-service/hermes/220602163764114483993251821.docx?Expires=2284879035&OSSAccessKeyId=LTAIZE6dJaBjRE47&Signature=ymOWxXlz7c8bPwESAEB0Vgq%2Fako%3D')
        // ChildVue.prototype.$downloadWatermarkFile('https://testoss.syounggroup.com/static/file/defaultTenantId/测试/测试PDF.pdf', 'asdadad.pdf')
        // ChildVue.prototype.$downloadWatermarkFile('https://oss.syounggroup.com/static/project/hermes/home-top-demo.png')
      }, 1000)
    },
    // 插入组件库
    appendComponents (ChildVue) {
      // ChildVue.component('qiankun-menu', MyMenu)
      // 所有项目统一升级组件库版本风险太大
      // ChildVue.use(lowcodelib, {
      //   BASE_URL: window.QIANKUN_DATA.BASE_URL,
      //   API_Header: window.QIANKUN_DATA.apiHeader
      // })
    },
    // 插入设置menu函数
    appendSetMenuFn (ChildVue, childVm) {
      const that = this
      ChildVue.prototype.$setMenu = async function (obj = {}) {
        // if (!obj.hideMenu) {
        //   await that.setuserinfo(obj.refreshToken, obj.userVo)
        // }
        // await that.setMenu({
        //   ChildVue,
        //   childVm,
        //   Menu: obj.Menu,
        //   hideMenu: obj.hideMenu
        // })
      }
      ChildVue.prototype.$hasPermission = function (code) {
        return window.QIANKUN_DATA.user_info.menus.find(
          (it) => it.code === code
        )
      }
    },
    setGlobState () {
      this.actions.setGlobalState(this.getGlobalState())
    },
    getGlobalState () {
      const { getCookie } = Vue.prototype.$utils
      // 兼容以前userInfo存值
      window.USER_INFO = this.USER_INFO
      return {
        user_info: this.USER_INFO,
        collapse: this.collapse,
        pageNav: this.pageNav,
        sessionId: getCookie(`${location.hostname}.mushroom.session.id`),
        userToken: this.token
      }
    },
    // 插入操作tabsFn
    appendTabsFn (childVm, ChildVue) {
      const that = this
      ChildVue.prototype.$redirect = function (path) {
        ChildVue.prototype.$reOpen(false, path)
      }
      ChildVue.prototype.$reOpen = function (closePath, path) {
        ChildVue.prototype.closeTabs(closePath)
        childVm.$router.push(path)
      }
      ChildVue.prototype.$back = function () {
        ChildVue.prototype.closeTabs()
        that.$router.back()
      }
      ChildVue.prototype.closeTabs = function (closePath) {
        if (that.tabList.length <= 1) {
          return
        }
        if (!closePath) {
          closePath = that.$route.hash.replace('#', '').split('?')[0] // 删除问号后面的参数,避免无法匹配
        }
        that.$store.commit('removeTabPageNav', closePath)
      }
      ChildVue.prototype.$closeTabs = function (path, cb) {
        if (that.tabList.length <= 1) {
          cb && cb()
          return
        }
        ChildVue.prototype.closeTabs(path)
        cb && cb()
        utils.afterCloseTabHandleRoute(that.$route, that.$router)
      }
    }
  }
}
</script>

<style lang="less">
@import url('./style/notebook.less');

html,
body {
  font-family: "PingFang SC";
  &::-webkit-scrollbar {
    height: 12px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background-color: #c6ccd7;
  }
}

body.qiankun-body {
  overflow-x: auto;
}

.qiankun-mainPart {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  white-space: nowrap;
  display: flex;
  background: linear-gradient(180deg, #D4E4FF 0%, #E0DBFF 100%);
  .qiankun-layout-left{
    transition: all 0.1s ease-in-out;
    background: linear-gradient(180deg, #D4E4FF 0%, #E0DBFF 100%);
  }
  .qiankun-layout-right{
    flex:1 1 auto;
    // flex: 0 0 calc(100vw - 71px);
    overflow: hidden;
    border-top-left-radius: 20px;
    box-shadow: 0 -8px 30px 0 rgba(139, 163, 210, 0.15);
    display: flex;
    flex-direction: column;
    .qiankun-content-inner{
      // width: 1000px;
      min-width: 1000px;
      overflow: auto;
    }
  }

  &.is-notebook {

    #vue,
    .qiankun-top-bar,
    .qiankun-core-menu {
      // zoom:0.9;
    }
  }

  &>.el-loading-mask {
    .el-loading-text {
      color: #0c1116;
    }
  }

  .qiankun-core-menu {
    flex: 0;
    flex-shrink: 0;
    flex-basis: 180px;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    display: inline-block;
    background-color: #0c1116;
    z-index: 2000;
    box-sizing: border-box;
    padding-bottom: 90px;

    &.collapse {
      flex-basis: 42px;
    }
  }

  .qiankun-core-content {
    display: flex;
    height: calc(100vh - 50px);

    .qiankun-content-view {
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow: auto;

      .qiankun-content-inner {
        flex: 1;
        box-sizing: border-box;
        overflow: hidden;

        #vue,
        #app,
        #vue>div {
          width: 100%;
          height: 100%;
          overflow: auto;
          box-sizing: border-box;
          background: linear-gradient(106deg, #EAEEFF 0.38%, #F0FDFF 42.75%, #E2EBFF 85.12%);
          white-space: initial;
          font-size: 14px;
        }
      }
    }
  }

  .fileUploadTest {
    position: absolute;
    top: 20px;
    left: 400px;
    width: 100px;
    height: 100px;
    z-index: 10000000;
  }
}
</style>
